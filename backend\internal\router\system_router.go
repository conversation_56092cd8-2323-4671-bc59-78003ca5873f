package router

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"wosm/internal/middleware"
	"wosm/internal/system"
)

// RegisterSystemRoutes 注册完整的企业级系统路由
func RegisterSystemRoutes(r *gin.RouterGroup, logger *zap.Logger, db *gorm.DB) {
	// 创建系统控制器实例
	loginController := system.NewSysLoginController(logger, db)
	userController := system.NewSysUserController(logger, db)
	roleController := system.NewSysRoleController(logger, db)
	menuController := system.NewSysMenuController(logger, db)
	deptController := system.NewSysDeptController(logger, db)
	postController := system.NewSysPostController(logger, db)
	dictTypeController := system.NewSysDictTypeController(logger, db)
	dictDataController := system.NewSysDictDataController(logger, db)
	configController := system.NewSysConfigController(logger, db)
	noticeController := system.NewSysNoticeController(logger, db)
	profileController := system.NewSysProfileController(logger, db)
	indexController := system.NewSysIndexController(logger, db)

	// 监控控制器
	operlogController := system.NewSysOperlogController(logger, db)
	logininforController := system.NewSysLogininforController(logger, db)
	userOnlineController := system.NewSysUserOnlineController(logger, db)
	serverController := system.NewServerController(logger, db)
	cacheController := system.NewCacheController(logger, db)

	// 定时任务控制器
	jobController := system.NewSysJobController(logger, db)
	jobLogController := system.NewSysJobLogController(logger, db)

	// 通用控制器
	commonController := system.NewCommonController(logger, db)

	// 无需认证的路由
	publicGroup := r.Group("")
	{
		// 登录相关
		loginController.RegisterRoutes(publicGroup)
		
		// 验证码
		publicGroup.GET("/captchaImage", loginController.GetCaptchaImage)
		
		// 首页信息
		indexController.RegisterRoutes(publicGroup)
	}

	// 需要认证的路由
	authGroup := r.Group("")
	authGroup.Use(middleware.JWTAuth())
	{
		// 用户信息相关
		authGroup.GET("/getInfo", loginController.GetInfo)
		authGroup.GET("/getRouters", loginController.GetRouters)
		authGroup.POST("/logout", loginController.Logout)

		// 系统管理模块
		systemGroup := authGroup.Group("/system")
		{
			// 用户管理
			userGroup := systemGroup.Group("/user")
			{
				userController.RegisterRoutes(userGroup)
			}

			// 角色管理
			roleGroup := systemGroup.Group("/role")
			{
				roleController.RegisterRoutes(roleGroup)
			}

			// 菜单管理
			menuGroup := systemGroup.Group("/menu")
			{
				menuController.RegisterRoutes(menuGroup)
			}

			// 部门管理
			deptGroup := systemGroup.Group("/dept")
			{
				deptController.RegisterRoutes(deptGroup)
			}

			// 岗位管理
			postGroup := systemGroup.Group("/post")
			{
				postController.RegisterRoutes(postGroup)
			}

			// 字典类型管理
			dictTypeGroup := systemGroup.Group("/dict/type")
			{
				dictTypeController.RegisterRoutes(dictTypeGroup)
			}

			// 字典数据管理
			dictDataGroup := systemGroup.Group("/dict/data")
			{
				dictDataController.RegisterRoutes(dictDataGroup)
			}

			// 参数配置管理
			configGroup := systemGroup.Group("/config")
			{
				configController.RegisterRoutes(configGroup)
			}

			// 通知公告管理
			noticeGroup := systemGroup.Group("/notice")
			{
				noticeController.RegisterRoutes(noticeGroup)
			}
		}

		// 监控管理模块
		monitorGroup := authGroup.Group("/monitor")
		{
			// 操作日志
			operlogGroup := monitorGroup.Group("/operlog")
			{
				operlogController.RegisterRoutes(operlogGroup)
			}

			// 登录日志
			logininforGroup := monitorGroup.Group("/logininfor")
			{
				logininforController.RegisterRoutes(logininforGroup)
			}

			// 在线用户
			onlineGroup := monitorGroup.Group("/online")
			{
				userOnlineController.RegisterRoutes(onlineGroup)
			}

			// 服务监控
			serverGroup := monitorGroup.Group("/server")
			{
				serverController.RegisterRoutes(serverGroup)
			}

			// 缓存监控
			cacheGroup := monitorGroup.Group("/cache")
			{
				cacheController.RegisterRoutes(cacheGroup)
			}
		}

		// 定时任务模块
		jobGroup := authGroup.Group("/monitor")
		{
			// 定时任务管理
			jobManageGroup := jobGroup.Group("/job")
			{
				jobController.RegisterRoutes(jobManageGroup)
			}

			// 任务执行日志
			jobLogGroup := jobGroup.Group("/jobLog")
			{
				jobLogController.RegisterRoutes(jobLogGroup)
			}
		}

		// 个人信息管理
		profileGroup := authGroup.Group("/system/user/profile")
		{
			profileController.RegisterRoutes(profileGroup)
		}

		// 通用功能
		commonGroup := authGroup.Group("/common")
		{
			commonController.RegisterRoutes(commonGroup)
		}
	}
}
