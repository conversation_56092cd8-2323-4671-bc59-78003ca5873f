package router

import (
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/ruoyi/backend/internal/domain"
	"github.com/ruoyi/backend/internal/middleware"
	"github.com/ruoyi/backend/internal/system"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// RegisterSimpleSystemRoutes 注册简化的系统路由
func RegisterSimpleSystemRoutes(r *gin.RouterGroup, logger *zap.Logger, db *gorm.DB) {
	// 无需认证的路由组
	publicGroup := r.Group("")
	{
		// 登录接口
		publicGroup.POST("/login", func(c *gin.Context) {
			var loginRequest struct {
				Username string `json:"username" binding:"required"`
				Password string `json:"password" binding:"required"`
				Code     string `json:"code"`
				UUID     string `json:"uuid"`
			}

			if err := c.ShouldBindJSO<PERSON>(&loginRequest); err != nil {
				c.JSON(http.StatusBadRequest, gin.H{
					"code": 400,
					"msg":  "参数错误",
				})
				return
			}

			// 简单的用户名密码验证
			if loginRequest.Username == "admin" && loginRequest.Password == "admin123" {
				// 生成JWT令牌
				token, err := middleware.GenerateToken(1, "admin")
				if err != nil {
					logger.Error("生成JWT令牌失败", zap.Error(err))
					c.JSON(http.StatusInternalServerError, gin.H{
						"code": 500,
						"msg":  "生成令牌失败",
					})
					return
				}

				c.JSON(http.StatusOK, gin.H{
					"code": 200,
					"msg":  "登录成功",
					"data": gin.H{
						"token": token,
					},
				})
			} else {
				c.JSON(http.StatusUnauthorized, gin.H{
					"code": 401,
					"msg":  "用户名或密码错误",
				})
			}
		})

		// 验证码接口
		publicGroup.GET("/captchaImage", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{
				"code": 200,
				"msg":  "操作成功",
				"data": gin.H{
					"uuid": "captcha-uuid-12345",
					"img":  "data:image/png;base64,demo-captcha-image",
				},
			})
		})
	}

	// 需要认证的路由组
	authGroup := r.Group("")
	authGroup.Use(middleware.JWTAuth())
	{
		// 获取用户信息
		authGroup.GET("/getInfo", func(c *gin.Context) {
			c.JSON(http.StatusOK, gin.H{
				"code": 200,
				"msg":  "操作成功",
				"data": gin.H{
					"user": gin.H{
						"userId":    1,
						"userName":  "管理员",
						"loginName": "admin",
						"email":     "<EMAIL>",
						"status":    "0",
					},
					"roles":       []string{"admin"},
					"permissions": []string{"*:*:*"},
				},
			})
		})

		// 获取路由信息
		authGroup.GET("/getRouters", func(c *gin.Context) {
			menus := []gin.H{
				{
					"name":      "System",
					"path":      "/system",
					"component": "Layout",
					"meta":      gin.H{"title": "系统管理", "icon": "system"},
					"children": []gin.H{
						{
							"name":      "User",
							"path":      "/system/user",
							"component": "system/user/index",
							"meta":      gin.H{"title": "用户管理", "icon": "user"},
						},
						{
							"name":      "Role",
							"path":      "/system/role",
							"component": "system/role/index",
							"meta":      gin.H{"title": "角色管理", "icon": "peoples"},
						},
						{
							"name":      "Menu",
							"path":      "/system/menu",
							"component": "system/menu/index",
							"meta":      gin.H{"title": "菜单管理", "icon": "tree-table"},
						},
					},
				},
			}
			c.JSON(http.StatusOK, gin.H{
				"code": 200,
				"msg":  "操作成功",
				"data": menus,
			})
		})

		// 系统管理路由组
		systemGroup := authGroup.Group("/system")
		{
			// 用户管理
			userGroup := systemGroup.Group("/user")
			{
				userGroup.GET("/list", func(c *gin.Context) {
					// 检查数据库连接
					if db == nil {
						c.JSON(http.StatusInternalServerError, gin.H{
							"code": 500,
							"msg":  "数据库连接失败",
						})
						return
					}

					// 查询用户列表
					var users []domain.SysUser
					var total int64

					// 构建查询条件
					query := db.Model(&domain.SysUser{}).Where("del_flag = ?", "0")

					// 获取总数
					if err := query.Count(&total).Error; err != nil {
						logger.Error("查询用户总数失败", zap.Error(err))
						c.JSON(http.StatusInternalServerError, gin.H{
							"code": 500,
							"msg":  "查询用户总数失败",
						})
						return
					}

					// 分页查询
					pageNum := 1
					pageSize := 10
					if page := c.Query("pageNum"); page != "" {
						if p, err := strconv.Atoi(page); err == nil && p > 0 {
							pageNum = p
						}
					}
					if size := c.Query("pageSize"); size != "" {
						if s, err := strconv.Atoi(size); err == nil && s > 0 {
							pageSize = s
						}
					}

					offset := (pageNum - 1) * pageSize
					if err := query.Offset(offset).Limit(pageSize).Find(&users).Error; err != nil {
						logger.Error("查询用户列表失败", zap.Error(err))
						c.JSON(http.StatusInternalServerError, gin.H{
							"code": 500,
							"msg":  "查询用户列表失败",
						})
						return
					}

					c.JSON(http.StatusOK, gin.H{
						"code": 200,
						"msg":  "操作成功",
						"data": gin.H{
							"rows":  users,
							"total": total,
						},
					})
				})

				userGroup.GET("/:userId", func(c *gin.Context) {
					userIdStr := c.Param("userId")
					userId, err := strconv.ParseInt(userIdStr, 10, 64)
					if err != nil {
						c.JSON(http.StatusBadRequest, gin.H{
							"code": 400,
							"msg":  "用户ID格式错误",
						})
						return
					}

					// 查询用户详情
					var user domain.SysUser
					if err := db.Where("user_id = ? AND del_flag = ?", userId, "0").First(&user).Error; err != nil {
						logger.Error("查询用户详情失败", zap.Error(err))
						c.JSON(http.StatusNotFound, gin.H{
							"code": 404,
							"msg":  "用户不存在",
						})
						return
					}

					c.JSON(http.StatusOK, gin.H{
						"code": 200,
						"msg":  "操作成功",
						"data": gin.H{
							"data": user,
							"roles": []gin.H{
								{"roleId": 1, "roleName": "超级管理员", "roleKey": "admin"},
							},
							"posts": []gin.H{
								{"postId": 1, "postName": "董事长", "postCode": "ceo"},
							},
						},
					})
				})

				userGroup.POST("", func(c *gin.Context) {
					var requestData struct {
						UserName    string `json:"userName"`
						LoginName   string `json:"loginName"`
						Email       string `json:"email"`
						Phonenumber string `json:"phonenumber"`
						Sex         string `json:"sex"`
						Status      string `json:"status"`
						DeptId      int64  `json:"deptId"`
						Password    string `json:"password"`
					}

					if err := c.ShouldBindJSON(&requestData); err != nil {
						c.JSON(http.StatusBadRequest, gin.H{
							"code": 400,
							"msg":  "参数错误: " + err.Error(),
						})
						return
					}

					// 创建用户对象，只使用数据库中存在的字段
					user := domain.SysUser{
						LoginName:   requestData.LoginName,
						UserName:    requestData.UserName,
						Email:       requestData.Email,
						Phonenumber: requestData.Phonenumber,
						Sex:         requestData.Sex,
						Status:      "0",
						DelFlag:     "0",
						DeptId:      requestData.DeptId,
						Password:    requestData.Password,
					}

					// 设置默认值
					if user.DeptId == 0 {
						user.DeptId = 103 // 默认部门
					}

					// 创建用户
					if err := db.Create(&user).Error; err != nil {
						logger.Error("创建用户失败", zap.Error(err))
						c.JSON(http.StatusInternalServerError, gin.H{
							"code": 500,
							"msg":  "创建用户失败: " + err.Error(),
						})
						return
					}

					c.JSON(http.StatusOK, gin.H{
						"code": 200,
						"msg":  "新增成功",
						"data": user,
					})
				})

				userGroup.PUT("", func(c *gin.Context) {
					var user domain.SysUser
					if err := c.ShouldBindJSON(&user); err != nil {
						c.JSON(http.StatusBadRequest, gin.H{
							"code": 400,
							"msg":  "参数错误: " + err.Error(),
						})
						return
					}

					// 验证字段长度
					if len(user.LoginName) > 30 {
						c.JSON(http.StatusBadRequest, gin.H{
							"code": 400,
							"msg":  "登录名长度不能超过30个字符",
						})
						return
					}
					if len(user.UserName) > 30 {
						c.JSON(http.StatusBadRequest, gin.H{
							"code": 400,
							"msg":  "用户名长度不能超过30个字符",
						})
						return
					}
					if len(user.Email) > 50 {
						c.JSON(http.StatusBadRequest, gin.H{
							"code": 400,
							"msg":  "邮箱长度不能超过50个字符",
						})
						return
					}
					if len(user.Phonenumber) > 11 {
						c.JSON(http.StatusBadRequest, gin.H{
							"code": 400,
							"msg":  "手机号长度不能超过11个字符",
						})
						return
					}

					// 检查用户是否存在
					var existingUser domain.SysUser
					if err := db.Where("user_id = ? AND del_flag = ?", user.UserId, "0").First(&existingUser).Error; err != nil {
						c.JSON(http.StatusNotFound, gin.H{
							"code": 404,
							"msg":  "用户不存在",
						})
						return
					}

					// 更新用户信息 - 只更新允许的字段
					updateData := map[string]interface{}{
						"dept_id":     user.DeptId,
						"login_name":  user.LoginName,
						"user_name":   user.UserName,
						"email":       user.Email,
						"phonenumber": user.Phonenumber,
						"sex":         user.Sex,
						"status":      user.Status,
					}

					// 如果提供了密码，也更新密码
					if user.Password != "" {
						updateData["password"] = user.Password
					}

					if err := db.Model(&existingUser).Updates(updateData).Error; err != nil {
						logger.Error("更新用户失败", zap.Error(err))
						c.JSON(http.StatusInternalServerError, gin.H{
							"code": 500,
							"msg":  "更新用户失败: " + err.Error(),
						})
						return
					}

					c.JSON(http.StatusOK, gin.H{
						"code": 200,
						"msg":  "修改成功",
						"data": user,
					})
				})

				userGroup.DELETE("/:userIds", func(c *gin.Context) {
					userIdsStr := c.Param("userIds")
					userIdStrs := strings.Split(userIdsStr, ",")

					var userIds []int64
					for _, idStr := range userIdStrs {
						if id, err := strconv.ParseInt(strings.TrimSpace(idStr), 10, 64); err == nil {
							userIds = append(userIds, id)
						}
					}

					if len(userIds) == 0 {
						c.JSON(http.StatusBadRequest, gin.H{
							"code": 400,
							"msg":  "用户ID格式错误",
						})
						return
					}

					// 软删除用户（设置del_flag为2）
					if err := db.Model(&domain.SysUser{}).Where("user_id IN ?", userIds).Update("del_flag", "2").Error; err != nil {
						logger.Error("删除用户失败", zap.Error(err))
						c.JSON(http.StatusInternalServerError, gin.H{
							"code": 500,
							"msg":  "删除用户失败",
						})
						return
					}

					c.JSON(http.StatusOK, gin.H{
						"code": 200,
						"msg":  "删除成功",
					})
				})
			}

			// 角色管理 - 使用完整的角色控制器
			roleGroup := systemGroup.Group("/role")
			{
				roleController := system.NewSysRoleController(logger, db)
				roleController.RegisterRoutes(roleGroup)
			}

			// 部门管理
			deptGroup := systemGroup.Group("/dept")
			{
				deptGroup.GET("/list", func(c *gin.Context) {
					c.JSON(http.StatusOK, gin.H{
						"code": 200,
						"msg":  "操作成功",
						"data": []gin.H{
							{
								"deptId":     100,
								"parentId":   0,
								"ancestors":  "0",
								"deptName":   "WOSM科技",
								"orderNum":   0,
								"leader":     "管理员",
								"phone":      "15888888888",
								"email":      "<EMAIL>",
								"status":     "0",
								"delFlag":    "0",
								"createTime": "2023-01-01 00:00:00",
								"children": []gin.H{
									{
										"deptId":     101,
										"parentId":   100,
										"ancestors":  "0,100",
										"deptName":   "深圳总公司",
										"orderNum":   1,
										"leader":     "张三",
										"phone":      "15888888888",
										"email":      "<EMAIL>",
										"status":     "0",
										"delFlag":    "0",
										"createTime": "2023-01-01 00:00:00",
										"children": []gin.H{
											{
												"deptId":     103,
												"parentId":   101,
												"ancestors":  "0,100,101",
												"deptName":   "研发部门",
												"orderNum":   1,
												"leader":     "李四",
												"phone":      "15888888888",
												"email":      "<EMAIL>",
												"status":     "0",
												"delFlag":    "0",
												"createTime": "2023-01-01 00:00:00",
											},
											{
												"deptId":     104,
												"parentId":   101,
												"ancestors":  "0,100,101",
												"deptName":   "市场部门",
												"orderNum":   2,
												"leader":     "王五",
												"phone":      "15888888888",
												"email":      "<EMAIL>",
												"status":     "0",
												"delFlag":    "0",
												"createTime": "2023-01-01 00:00:00",
											},
										},
									},
									{
										"deptId":     102,
										"parentId":   100,
										"ancestors":  "0,100",
										"deptName":   "长沙分公司",
										"orderNum":   2,
										"leader":     "赵六",
										"phone":      "15888888888",
										"email":      "<EMAIL>",
										"status":     "0",
										"delFlag":    "0",
										"createTime": "2023-01-01 00:00:00",
									},
								},
							},
						},
					})
				})

				deptGroup.GET("/list/exclude/:deptId", func(c *gin.Context) {
					deptId := c.Param("deptId")
					c.JSON(http.StatusOK, gin.H{
						"code": 200,
						"msg":  "操作成功",
						"data": []gin.H{
							{
								"deptId":     100,
								"parentId":   0,
								"ancestors":  "0",
								"deptName":   "WOSM科技",
								"orderNum":   0,
								"leader":     "管理员",
								"phone":      "15888888888",
								"email":      "<EMAIL>",
								"status":     "0",
								"delFlag":    "0",
								"createTime": "2023-01-01 00:00:00",
								"note":       "排除部门ID: " + deptId,
							},
						},
					})
				})

				deptGroup.GET("/:deptId", func(c *gin.Context) {
					deptId := c.Param("deptId")
					c.JSON(http.StatusOK, gin.H{
						"code": 200,
						"msg":  "操作成功",
						"data": gin.H{
							"deptId":     103,
							"parentId":   101,
							"ancestors":  "0,100,101",
							"deptName":   "研发部门",
							"orderNum":   1,
							"leader":     "李四",
							"phone":      "15888888888",
							"email":      "<EMAIL>",
							"status":     "0",
							"delFlag":    "0",
							"createTime": "2023-01-01 00:00:00",
							"note":       "部门ID: " + deptId,
						},
					})
				})

				deptGroup.POST("", func(c *gin.Context) {
					var dept map[string]interface{}
					if err := c.ShouldBindJSON(&dept); err != nil {
						c.JSON(http.StatusBadRequest, gin.H{
							"code": 400,
							"msg":  "参数错误",
						})
						return
					}

					c.JSON(http.StatusOK, gin.H{
						"code": 200,
						"msg":  "新增成功",
						"data": dept,
					})
				})

				deptGroup.PUT("", func(c *gin.Context) {
					var dept map[string]interface{}
					if err := c.ShouldBindJSON(&dept); err != nil {
						c.JSON(http.StatusBadRequest, gin.H{
							"code": 400,
							"msg":  "参数错误",
						})
						return
					}

					c.JSON(http.StatusOK, gin.H{
						"code": 200,
						"msg":  "修改成功",
						"data": dept,
					})
				})

				deptGroup.DELETE("/:deptId", func(c *gin.Context) {
					deptId := c.Param("deptId")
					c.JSON(http.StatusOK, gin.H{
						"code": 200,
						"msg":  "删除成功",
						"data": gin.H{
							"deletedDeptId": deptId,
						},
					})
				})
			}

			// 岗位管理
			postGroup := systemGroup.Group("/post")
			{
				postGroup.GET("/list", func(c *gin.Context) {
					c.JSON(http.StatusOK, gin.H{
						"code": 200,
						"msg":  "操作成功",
						"data": gin.H{
							"total": 4,
							"rows": []gin.H{
								{
									"postId":     1,
									"postCode":   "ceo",
									"postName":   "董事长",
									"postSort":   1,
									"status":     "0",
									"createTime": "2023-01-01 00:00:00",
								},
								{
									"postId":     2,
									"postCode":   "se",
									"postName":   "项目经理",
									"postSort":   2,
									"status":     "0",
									"createTime": "2023-01-01 00:00:00",
								},
								{
									"postId":     3,
									"postCode":   "hr",
									"postName":   "人力资源",
									"postSort":   3,
									"status":     "0",
									"createTime": "2023-01-01 00:00:00",
								},
								{
									"postId":     4,
									"postCode":   "user",
									"postName":   "普通员工",
									"postSort":   4,
									"status":     "0",
									"createTime": "2023-01-01 00:00:00",
								},
							},
						},
					})
				})

				postGroup.GET("/:postId", func(c *gin.Context) {
					postId := c.Param("postId")
					c.JSON(http.StatusOK, gin.H{
						"code": 200,
						"msg":  "操作成功",
						"data": gin.H{
							"postId":     1,
							"postCode":   "ceo",
							"postName":   "董事长",
							"postSort":   1,
							"status":     "0",
							"createTime": "2023-01-01 00:00:00",
							"note":       "岗位ID: " + postId,
						},
					})
				})

				postGroup.POST("", func(c *gin.Context) {
					var post map[string]interface{}
					if err := c.ShouldBindJSON(&post); err != nil {
						c.JSON(http.StatusBadRequest, gin.H{
							"code": 400,
							"msg":  "参数错误",
						})
						return
					}

					c.JSON(http.StatusOK, gin.H{
						"code": 200,
						"msg":  "新增成功",
						"data": post,
					})
				})

				postGroup.PUT("", func(c *gin.Context) {
					var post map[string]interface{}
					if err := c.ShouldBindJSON(&post); err != nil {
						c.JSON(http.StatusBadRequest, gin.H{
							"code": 400,
							"msg":  "参数错误",
						})
						return
					}

					c.JSON(http.StatusOK, gin.H{
						"code": 200,
						"msg":  "修改成功",
						"data": post,
					})
				})

				postGroup.DELETE("/:postIds", func(c *gin.Context) {
					postIds := c.Param("postIds")
					c.JSON(http.StatusOK, gin.H{
						"code": 200,
						"msg":  "删除成功",
						"data": gin.H{
							"deletedPostIds": postIds,
						},
					})
				})
			}

			// 字典类型管理
			dictTypeGroup := systemGroup.Group("/dict/type")
			{
				dictTypeGroup.GET("/list", func(c *gin.Context) {
					c.JSON(http.StatusOK, gin.H{
						"code": 200,
						"msg":  "操作成功",
						"data": gin.H{
							"total": 3,
							"rows": []gin.H{
								{
									"dictId":     1,
									"dictName":   "用户性别",
									"dictType":   "sys_user_sex",
									"status":     "0",
									"createTime": "2023-01-01 00:00:00",
								},
								{
									"dictId":     2,
									"dictName":   "菜单状态",
									"dictType":   "sys_show_hide",
									"status":     "0",
									"createTime": "2023-01-01 00:00:00",
								},
								{
									"dictId":     3,
									"dictName":   "系统状态",
									"dictType":   "sys_normal_disable",
									"status":     "0",
									"createTime": "2023-01-01 00:00:00",
								},
							},
						},
					})
				})

				dictTypeGroup.GET("/:dictId", func(c *gin.Context) {
					dictId := c.Param("dictId")
					c.JSON(http.StatusOK, gin.H{
						"code": 200,
						"msg":  "操作成功",
						"data": gin.H{
							"dictId":     1,
							"dictName":   "用户性别",
							"dictType":   "sys_user_sex",
							"status":     "0",
							"createTime": "2023-01-01 00:00:00",
							"note":       "字典ID: " + dictId,
						},
					})
				})

				dictTypeGroup.POST("", func(c *gin.Context) {
					var dictType map[string]interface{}
					if err := c.ShouldBindJSON(&dictType); err != nil {
						c.JSON(http.StatusBadRequest, gin.H{
							"code": 400,
							"msg":  "参数错误",
						})
						return
					}

					c.JSON(http.StatusOK, gin.H{
						"code": 200,
						"msg":  "新增成功",
						"data": dictType,
					})
				})

				dictTypeGroup.PUT("", func(c *gin.Context) {
					var dictType map[string]interface{}
					if err := c.ShouldBindJSON(&dictType); err != nil {
						c.JSON(http.StatusBadRequest, gin.H{
							"code": 400,
							"msg":  "参数错误",
						})
						return
					}

					c.JSON(http.StatusOK, gin.H{
						"code": 200,
						"msg":  "修改成功",
						"data": dictType,
					})
				})

				dictTypeGroup.DELETE("/:dictIds", func(c *gin.Context) {
					dictIds := c.Param("dictIds")
					c.JSON(http.StatusOK, gin.H{
						"code": 200,
						"msg":  "删除成功",
						"data": gin.H{
							"deletedDictIds": dictIds,
						},
					})
				})

				dictTypeGroup.GET("/optionselect", func(c *gin.Context) {
					c.JSON(http.StatusOK, gin.H{
						"code": 200,
						"msg":  "操作成功",
						"data": []gin.H{
							{
								"dictId":   1,
								"dictName": "用户性别",
								"dictType": "sys_user_sex",
							},
							{
								"dictId":   2,
								"dictName": "菜单状态",
								"dictType": "sys_show_hide",
							},
							{
								"dictId":   3,
								"dictName": "系统状态",
								"dictType": "sys_normal_disable",
							},
						},
					})
				})
			}

			// 菜单管理
			menuGroup := systemGroup.Group("/menu")
			{
				menuGroup.GET("/list", func(c *gin.Context) {
					c.JSON(http.StatusOK, gin.H{
						"code": 200,
						"msg":  "操作成功",
						"data": []gin.H{
							{
								"menuId":   1,
								"menuName": "系统管理",
								"parentId": 0,
								"orderNum": 1,
								"path":     "/system",
								"menuType": "M",
								"visible":  "0",
								"status":   "0",
							},
							{
								"menuId":   2,
								"menuName": "用户管理",
								"parentId": 1,
								"orderNum": 1,
								"path":     "/system/user",
								"menuType": "C",
								"visible":  "0",
								"status":   "0",
							},
						},
					})
				})
			}
		}
	}
}
