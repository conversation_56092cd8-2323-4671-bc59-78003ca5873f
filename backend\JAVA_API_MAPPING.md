# Java后端API对照表

## 🎯 目的
确保Go后端的每个API都与Java后端完全对应，不遗漏任何功能。

## 📋 系统管理模块

### 用户管理 (SysUserController.java)
| Java方法 | URL | HTTP方法 | Go实现状态 | 备注 |
|---------|-----|----------|-----------|------|
| list() | /system/user/list | GET | ✅已实现 | 用户列表查询 |
| getInfo() | /system/user/{userId} | GET | ✅已实现 | 获取用户详情 |
| add() | /system/user | POST | ✅已实现 | 新增用户 |
| edit() | /system/user | PUT | ✅已实现 | 修改用户 |
| remove() | /system/user/{userIds} | DELETE | ✅已实现 | 删除用户 |
| resetPwd() | /system/user/resetPwd | PUT | ✅已实现 | 重置密码 |
| changeStatus() | /system/user/changeStatus | PUT | ✅已实现 | 修改状态 |
| export() | /system/user/export | POST | ⏳待实现 | 导出用户 |
| importData() | /system/user/importData | POST | ⏳待实现 | 导入用户 |
| importTemplate() | /system/user/importTemplate | POST | ⏳待实现 | 导入模板 |
| authRole() | /system/user/authRole/{userId} | GET | ⏳待实现 | 用户授权角色 |
| insertAuthRole() | /system/user/authRole | PUT | ⏳待实现 | 用户授权角色保存 |
| deptTree() | /system/user/deptTree | GET | ⏳待实现 | 部门树数据 |

### 角色管理 (SysRoleController.java)
| Java方法 | URL | HTTP方法 | Go实现状态 | 备注 |
|---------|-----|----------|-----------|------|
| list() | /system/role/list | GET | ✅已实现 | 角色列表查询 |
| getInfo() | /system/role/{roleId} | GET | ✅已实现 | 获取角色详情 |
| add() | /system/role | POST | ✅已实现 | 新增角色 |
| edit() | /system/role | PUT | ✅已实现 | 修改角色 |
| dataScope() | /system/role/dataScope | PUT | ✅已实现 | 数据权限 |
| changeStatus() | /system/role/changeStatus | PUT | ✅已实现 | 修改状态 |
| remove() | /system/role/{roleIds} | DELETE | ✅已实现 | 删除角色 |
| optionselect() | /system/role/optionselect | GET | ✅已实现 | 角色选择框 |
| export() | /system/role/export | POST | 🔄部分实现 | 导出角色 |
| allocatedList() | /system/role/authUser/allocatedList | GET | 🔄部分实现 | 已分配用户 |
| unallocatedList() | /system/role/authUser/unallocatedList | GET | 🔄部分实现 | 未分配用户 |
| cancelAuthUser() | /system/role/authUser/cancel | PUT | 🔄部分实现 | 取消授权 |
| cancelAuthUserAll() | /system/role/authUser/cancelAll | PUT | 🔄部分实现 | 批量取消授权 |
| selectAuthUserAll() | /system/role/authUser/selectAll | PUT | 🔄部分实现 | 批量选择授权 |
| deptTree() | /system/role/deptTree/{roleId} | GET | 🔄部分实现 | 角色部门树 |

### 菜单管理 (SysMenuController.java)
| Java方法 | URL | HTTP方法 | Go实现状态 | 备注 |
|---------|-----|----------|-----------|------|
| list() | /system/menu/list | GET | 🔄部分实现 | 菜单列表 |
| getInfo() | /system/menu/{menuId} | GET | ❌未实现 | 获取菜单详情 |
| add() | /system/menu | POST | ❌未实现 | 新增菜单 |
| edit() | /system/menu | PUT | ❌未实现 | 修改菜单 |
| remove() | /system/menu/{menuId} | DELETE | ❌未实现 | 删除菜单 |
| treeselect() | /system/menu/treeselect | GET | ❌未实现 | 菜单树选择 |
| roleMenuTreeselect() | /system/menu/roleMenuTreeselect/{roleId} | GET | ❌未实现 | 角色菜单树 |

### 部门管理 (SysDeptController.java)
| Java方法 | URL | HTTP方法 | Go实现状态 | 备注 |
|---------|-----|----------|-----------|------|
| list() | /system/dept/list | GET | 🔄部分实现 | 部门列表 |
| getInfo() | /system/dept/{deptId} | GET | ❌未实现 | 获取部门详情 |
| add() | /system/dept | POST | ❌未实现 | 新增部门 |
| edit() | /system/dept | PUT | ❌未实现 | 修改部门 |
| remove() | /system/dept/{deptId} | DELETE | ❌未实现 | 删除部门 |
| exclude() | /system/dept/list/exclude/{deptId} | GET | ❌未实现 | 排除节点 |
| treeselect() | /system/dept/treeselect | GET | ❌未实现 | 部门树选择 |
| roleDeptTreeselect() | /system/dept/roleDeptTreeselect/{roleId} | GET | ❌未实现 | 角色部门树 |

### 岗位管理 (SysPostController.java)
| Java方法 | URL | HTTP方法 | Go实现状态 | 备注 |
|---------|-----|----------|-----------|------|
| list() | /system/post/list | GET | ❌未实现 | 岗位列表 |
| getInfo() | /system/post/{postId} | GET | ❌未实现 | 获取岗位详情 |
| add() | /system/post | POST | ❌未实现 | 新增岗位 |
| edit() | /system/post | PUT | ❌未实现 | 修改岗位 |
| remove() | /system/post/{postIds} | DELETE | ❌未实现 | 删除岗位 |
| export() | /system/post/export | POST | ❌未实现 | 导出岗位 |
| optionselect() | /system/post/optionselect | GET | ❌未实现 | 岗位选择框 |

### 字典类型管理 (SysDictTypeController.java)
| Java方法 | URL | HTTP方法 | Go实现状态 | 备注 |
|---------|-----|----------|-----------|------|
| list() | /system/dict/type/list | GET | ❌未实现 | 字典类型列表 |
| getInfo() | /system/dict/type/{dictId} | GET | ❌未实现 | 获取字典类型详情 |
| add() | /system/dict/type | POST | ❌未实现 | 新增字典类型 |
| edit() | /system/dict/type | PUT | ❌未实现 | 修改字典类型 |
| remove() | /system/dict/type/{dictIds} | DELETE | ❌未实现 | 删除字典类型 |
| export() | /system/dict/type/export | POST | ❌未实现 | 导出字典类型 |
| optionselect() | /system/dict/type/optionselect | GET | ❌未实现 | 字典选择框 |
| refreshCache() | /system/dict/type/refreshCache | DELETE | ❌未实现 | 刷新缓存 |

### 字典数据管理 (SysDictDataController.java)
| Java方法 | URL | HTTP方法 | Go实现状态 | 备注 |
|---------|-----|----------|-----------|------|
| list() | /system/dict/data/list | GET | ❌未实现 | 字典数据列表 |
| getInfo() | /system/dict/data/{dictCode} | GET | ❌未实现 | 获取字典数据详情 |
| dictType() | /system/dict/data/type/{dictType} | GET | ❌未实现 | 根据字典类型查询 |
| add() | /system/dict/data | POST | ❌未实现 | 新增字典数据 |
| edit() | /system/dict/data | PUT | ❌未实现 | 修改字典数据 |
| remove() | /system/dict/data/{dictCodes} | DELETE | ❌未实现 | 删除字典数据 |
| export() | /system/dict/data/export | POST | ❌未实现 | 导出字典数据 |

### 参数配置管理 (SysConfigController.java)
| Java方法 | URL | HTTP方法 | Go实现状态 | 备注 |
|---------|-----|----------|-----------|------|
| list() | /system/config/list | GET | ❌未实现 | 参数列表 |
| getInfo() | /system/config/{configId} | GET | ❌未实现 | 获取参数详情 |
| getConfigKey() | /system/config/configKey/{configKey} | GET | ❌未实现 | 根据参数键名查询 |
| add() | /system/config | POST | ❌未实现 | 新增参数 |
| edit() | /system/config | PUT | ❌未实现 | 修改参数 |
| remove() | /system/config/{configIds} | DELETE | ❌未实现 | 删除参数 |
| export() | /system/config/export | POST | ❌未实现 | 导出参数 |
| refreshCache() | /system/config/refreshCache | DELETE | ❌未实现 | 刷新缓存 |

### 通知公告管理 (SysNoticeController.java)
| Java方法 | URL | HTTP方法 | Go实现状态 | 备注 |
|---------|-----|----------|-----------|------|
| list() | /system/notice/list | GET | ❌未实现 | 公告列表 |
| getInfo() | /system/notice/{noticeId} | GET | ❌未实现 | 获取公告详情 |
| add() | /system/notice | POST | ❌未实现 | 新增公告 |
| edit() | /system/notice | PUT | ❌未实现 | 修改公告 |
| remove() | /system/notice/{noticeIds} | DELETE | ❌未实现 | 删除公告 |

## 📋 监控管理模块

### 操作日志 (SysOperlogController.java)
| Java方法 | URL | HTTP方法 | Go实现状态 | 备注 |
|---------|-----|----------|-----------|------|
| list() | /monitor/operlog/list | GET | ❌未实现 | 操作日志列表 |
| remove() | /monitor/operlog/{operIds} | DELETE | ❌未实现 | 删除操作日志 |
| export() | /monitor/operlog/export | POST | ❌未实现 | 导出操作日志 |
| clean() | /monitor/operlog/clean | DELETE | ❌未实现 | 清空操作日志 |

### 登录日志 (SysLogininforController.java)
| Java方法 | URL | HTTP方法 | Go实现状态 | 备注 |
|---------|-----|----------|-----------|------|
| list() | /monitor/logininfor/list | GET | ❌未实现 | 登录日志列表 |
| remove() | /monitor/logininfor/{infoIds} | DELETE | ❌未实现 | 删除登录日志 |
| export() | /monitor/logininfor/export | POST | ❌未实现 | 导出登录日志 |
| clean() | /monitor/logininfor/clean | DELETE | ❌未实现 | 清空登录日志 |
| unlock() | /monitor/logininfor/unlock/{userName} | GET | ❌未实现 | 解锁用户 |

### 在线用户 (SysUserOnlineController.java)
| Java方法 | URL | HTTP方法 | Go实现状态 | 备注 |
|---------|-----|----------|-----------|------|
| list() | /monitor/online/list | GET | ❌未实现 | 在线用户列表 |
| forceLogout() | /monitor/online/{tokenId} | DELETE | ❌未实现 | 强制退出 |

### 服务监控 (ServerController.java)
| Java方法 | URL | HTTP方法 | Go实现状态 | 备注 |
|---------|-----|----------|-----------|------|
| getInfo() | /monitor/server | GET | ❌未实现 | 服务器信息 |

### 缓存监控 (CacheController.java)
| Java方法 | URL | HTTP方法 | Go实现状态 | 备注 |
|---------|-----|----------|-----------|------|
| getInfo() | /monitor/cache | GET | ❌未实现 | 缓存信息 |
| getNames() | /monitor/cache/getNames | GET | ❌未实现 | 缓存名称 |
| getKeys() | /monitor/cache/getKeys/{cacheName} | GET | ❌未实现 | 缓存键名 |
| getValue() | /monitor/cache/getValue/{cacheName}/{cacheKey} | GET | ❌未实现 | 缓存内容 |
| clearCacheName() | /monitor/cache/clearCacheName/{cacheName} | DELETE | ❌未实现 | 清理缓存名称 |
| clearCacheKey() | /monitor/cache/clearCacheKey/{cacheKey} | DELETE | ❌未实现 | 清理缓存键名 |
| clearCacheAll() | /monitor/cache/clearCacheAll | DELETE | ❌未实现 | 清理全部缓存 |

## 📋 定时任务模块

### 定时任务 (SysJobController.java)
| Java方法 | URL | HTTP方法 | Go实现状态 | 备注 |
|---------|-----|----------|-----------|------|
| list() | /monitor/job/list | GET | ❌未实现 | 任务列表 |
| getInfo() | /monitor/job/{jobId} | GET | ❌未实现 | 获取任务详情 |
| add() | /monitor/job | POST | ❌未实现 | 新增任务 |
| edit() | /monitor/job | PUT | ❌未实现 | 修改任务 |
| remove() | /monitor/job/{jobIds} | DELETE | ❌未实现 | 删除任务 |
| changeStatus() | /monitor/job/changeStatus | PUT | ❌未实现 | 任务状态修改 |
| run() | /monitor/job/run | PUT | ❌未实现 | 任务立即执行 |
| export() | /monitor/job/export | POST | ❌未实现 | 导出任务 |

### 任务日志 (SysJobLogController.java)
| Java方法 | URL | HTTP方法 | Go实现状态 | 备注 |
|---------|-----|----------|-----------|------|
| list() | /monitor/jobLog/list | GET | ❌未实现 | 任务日志列表 |
| getInfo() | /monitor/jobLog/{jobLogId} | GET | ❌未实现 | 获取任务日志详情 |
| remove() | /monitor/jobLog/{jobLogIds} | DELETE | ❌未实现 | 删除任务日志 |
| export() | /monitor/jobLog/export | POST | ❌未实现 | 导出任务日志 |
| clean() | /monitor/jobLog/clean | DELETE | ❌未实现 | 清空任务日志 |

## 📊 实现状态统计

- ✅ 已实现：19个API
- 🔄 部分实现：8个API
- ❌ 未实现：68个API
- ⏳ 待实现：3个API

**总计：98个API需要实现**

### 🎉 **重大进展：角色管理模块完成！**
- ✅ 角色管理15个API全部实现
- ✅ 完全按照Java后端业务逻辑
- ✅ API测试验证通过
- ✅ 响应格式与Java后端一致

## 🎯 优先级排序

### 高优先级（前端核心功能）
1. 角色管理 - 权限控制核心
2. 菜单管理 - 动态路由核心
3. 部门管理 - 数据权限核心

### 中优先级（系统管理功能）
4. 岗位管理
5. 字典管理
6. 参数配置

### 低优先级（监控和日志）
7. 操作日志
8. 登录日志
9. 在线用户
10. 系统监控
11. 定时任务

---

**重要：每实现一个API都要在此表格中更新状态，确保不遗漏任何功能！**
