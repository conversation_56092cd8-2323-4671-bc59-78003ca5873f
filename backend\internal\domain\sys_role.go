package domain

// SysRole 角色表 sys_role
type SysRole struct {
	BaseEntity

	// 角色ID
	RoleId int64 `json:"roleId" gorm:"column:role_id;primary_key"`

	// 角色名称
	RoleName string `json:"roleName" gorm:"column:role_name" validate:"required,max=30" label:"角色名称"`

	// 角色权限字符串
	RoleKey string `json:"roleKey" gorm:"column:role_key" validate:"required,max=100" label:"权限字符"`

	// 显示顺序
	RoleSort int `json:"roleSort" gorm:"column:role_sort" validate:"required" label:"显示顺序"`

	// 数据范围（1：所有数据权限；2：自定义数据权限；3：本部门数据权限；4：本部门及以下数据权限；5：仅本人数据权限）
	DataScope string `json:"dataScope" gorm:"column:data_scope"`

	// 菜单树选择项是否关联显示（0：父子不互相关联显示 1：父子互相关联显示）
	MenuCheckStrictly bool `json:"menuCheckStrictly" gorm:"column:menu_check_strictly"`

	// 部门树选择项是否关联显示（0：父子不互相关联显示 1：父子互相关联显示）
	DeptCheckStrictly bool `json:"deptCheckStrictly" gorm:"column:dept_check_strictly"`

	// 角色状态（0正常 1停用）
	Status string `json:"status" gorm:"column:status"`

	// 删除标志（0代表存在 2代表删除）
	DelFlag string `json:"delFlag" gorm:"column:del_flag"`

	// 菜单组
	MenuIds []int64 `json:"menuIds" gorm:"-"`

	// 部门组（数据权限）
	DeptIds []int64 `json:"deptIds" gorm:"-"`

	// 用户是否存在此角色标识
	Flag bool `json:"flag" gorm:"-"`

	// 角色菜单权限
	Permissions []string `json:"permissions" gorm:"-"`
}

// TableName 设置表名
func (SysRole) TableName() string {
	return "sys_role"
}
