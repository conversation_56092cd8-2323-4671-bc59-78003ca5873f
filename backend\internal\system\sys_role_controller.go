package system

import (
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"wosm/internal/controller"
	"wosm/internal/domain"
)

// SysRoleController 角色信息控制器
type SysRoleController struct {
	controller.BaseController
	db *gorm.DB
}

// NewSysRoleController 创建角色控制器
func NewSysRoleController(logger *zap.Logger, db *gorm.DB) *SysRoleController {
	return &SysRoleController{
		BaseController: controller.BaseController{
			Logger: logger,
		},
		db: db,
	}
}

// RegisterRoutes 注册路由
func (c *SysRoleController) RegisterRoutes(r *gin.RouterGroup) {
	r.GET("/list", c.List)
	r.POST("/export", c.Export)
	r.GET("/:roleId", c.GetInfo)
	r.POST("", c.Add)
	r.PUT("", c.Edit)
	r.PUT("/dataScope", c.DataScope)
	r.PUT("/changeStatus", c.ChangeStatus)
	r.DELETE("/:roleIds", c.Remove)
	r.GET("/optionselect", c.Optionselect)
	r.GET("/authUser/allocatedList", c.AllocatedList)
	r.GET("/authUser/unallocatedList", c.UnallocatedList)
	r.PUT("/authUser/cancel", c.CancelAuthUser)
	r.PUT("/authUser/cancelAll", c.CancelAuthUserAll)
	r.PUT("/authUser/selectAll", c.SelectAuthUserAll)
	r.GET("/deptTree/:roleId", c.DeptTree)
}

// List 获取角色列表
func (c *SysRoleController) List(ctx *gin.Context) {
	var roles []domain.SysRole
	
	// 构建查询
	query := c.db.Where("del_flag = ?", "0")
	
	// 添加查询条件
	if roleName := ctx.Query("roleName"); roleName != "" {
		query = query.Where("role_name LIKE ?", "%"+roleName+"%")
	}
	if roleKey := ctx.Query("roleKey"); roleKey != "" {
		query = query.Where("role_key LIKE ?", "%"+roleKey+"%")
	}
	if status := ctx.Query("status"); status != "" {
		query = query.Where("status = ?", status)
	}
	
	// 执行查询
	if err := query.Find(&roles).Error; err != nil {
		c.Logger.Error("查询角色列表失败", zap.Error(err))
		c.ErrorWithMessage(ctx, "查询失败")
		return
	}
	
	// 返回结果
	c.SuccessWithData(ctx, map[string]interface{}{
		"total": len(roles),
		"rows":  roles,
	})
}

// Export 导出角色
func (c *SysRoleController) Export(ctx *gin.Context) {
	c.SuccessWithMessage(ctx, "导出功能开发中")
}

// GetInfo 根据角色编号获取详细信息
func (c *SysRoleController) GetInfo(ctx *gin.Context) {
	roleIdStr := ctx.Param("roleId")
	roleId, err := strconv.ParseInt(roleIdStr, 10, 64)
	if err != nil {
		c.ErrorWithMessage(ctx, "角色ID格式错误")
		return
	}

	var role domain.SysRole
	if err := c.db.Where("role_id = ? AND del_flag = ?", roleId, "0").First(&role).Error; err != nil {
		c.Logger.Error("查询角色信息失败", zap.Error(err))
		c.ErrorWithMessage(ctx, "角色不存在")
		return
	}

	c.SuccessWithData(ctx, role)
}

// Add 新增角色
func (c *SysRoleController) Add(ctx *gin.Context) {
	var role domain.SysRole
	if err := ctx.ShouldBindJSON(&role); err != nil {
		c.ErrorWithMessage(ctx, "参数错误")
		return
	}

	// 检查角色名称唯一性
	var count int64
	c.db.Model(&domain.SysRole{}).Where("role_name = ? AND del_flag = ?", role.RoleName, "0").Count(&count)
	if count > 0 {
		c.ErrorWithMessage(ctx, "角色名称已存在")
		return
	}

	// 检查角色权限唯一性
	c.db.Model(&domain.SysRole{}).Where("role_key = ? AND del_flag = ?", role.RoleKey, "0").Count(&count)
	if count > 0 {
		c.ErrorWithMessage(ctx, "角色权限已存在")
		return
	}

	// 设置默认值
	role.DelFlag = "0"
	role.CreateBy = c.GetUsername(ctx)

	// 保存角色
	if err := c.db.Create(&role).Error; err != nil {
		c.Logger.Error("新增角色失败", zap.Error(err))
		c.ErrorWithMessage(ctx, "新增失败")
		return
	}

	c.SuccessWithMessage(ctx, "新增成功")
}

// Edit 修改保存角色
func (c *SysRoleController) Edit(ctx *gin.Context) {
	var role domain.SysRole
	if err := ctx.ShouldBindJSON(&role); err != nil {
		c.ErrorWithMessage(ctx, "参数错误")
		return
	}

	// 检查角色是否存在
	var existingRole domain.SysRole
	if err := c.db.Where("role_id = ? AND del_flag = ?", role.RoleId, "0").First(&existingRole).Error; err != nil {
		c.ErrorWithMessage(ctx, "角色不存在")
		return
	}

	// 检查角色名称唯一性（排除自己）
	var count int64
	c.db.Model(&domain.SysRole{}).Where("role_name = ? AND role_id != ? AND del_flag = ?", role.RoleName, role.RoleId, "0").Count(&count)
	if count > 0 {
		c.ErrorWithMessage(ctx, "角色名称已存在")
		return
	}

	// 检查角色权限唯一性（排除自己）
	c.db.Model(&domain.SysRole{}).Where("role_key = ? AND role_id != ? AND del_flag = ?", role.RoleKey, role.RoleId, "0").Count(&count)
	if count > 0 {
		c.ErrorWithMessage(ctx, "角色权限已存在")
		return
	}

	// 设置更新者
	role.UpdateBy = c.GetUsername(ctx)

	// 更新角色
	if err := c.db.Model(&existingRole).Updates(&role).Error; err != nil {
		c.Logger.Error("修改角色失败", zap.Error(err))
		c.ErrorWithMessage(ctx, "修改失败")
		return
	}

	c.SuccessWithMessage(ctx, "修改成功")
}

// DataScope 修改保存数据权限
func (c *SysRoleController) DataScope(ctx *gin.Context) {
	var role domain.SysRole
	if err := ctx.ShouldBindJSON(&role); err != nil {
		c.ErrorWithMessage(ctx, "参数错误")
		return
	}

	// 更新数据权限
	if err := c.db.Model(&domain.SysRole{}).Where("role_id = ?", role.RoleId).Update("data_scope", role.DataScope).Error; err != nil {
		c.Logger.Error("修改数据权限失败", zap.Error(err))
		c.ErrorWithMessage(ctx, "修改失败")
		return
	}

	c.SuccessWithMessage(ctx, "修改成功")
}

// ChangeStatus 状态修改
func (c *SysRoleController) ChangeStatus(ctx *gin.Context) {
	var role domain.SysRole
	if err := ctx.ShouldBindJSON(&role); err != nil {
		c.ErrorWithMessage(ctx, "参数错误")
		return
	}

	// 更新状态
	if err := c.db.Model(&domain.SysRole{}).Where("role_id = ?", role.RoleId).Updates(map[string]interface{}{
		"status":    role.Status,
		"update_by": c.GetUsername(ctx),
	}).Error; err != nil {
		c.Logger.Error("修改角色状态失败", zap.Error(err))
		c.ErrorWithMessage(ctx, "修改失败")
		return
	}

	c.SuccessWithMessage(ctx, "修改成功")
}

// Remove 删除角色
func (c *SysRoleController) Remove(ctx *gin.Context) {
	roleIdsStr := ctx.Param("roleIds")
	roleIds := strings.Split(roleIdsStr, ",")

	// 将字符串ID转换为int64数组
	ids := make([]int64, 0, len(roleIds))
	for _, idStr := range roleIds {
		id, err := strconv.ParseInt(idStr, 10, 64)
		if err != nil {
			continue
		}
		ids = append(ids, id)
	}

	// 软删除角色
	if err := c.db.Model(&domain.SysRole{}).Where("role_id IN ?", ids).Updates(map[string]interface{}{
		"del_flag":  "2",
		"update_by": c.GetUsername(ctx),
	}).Error; err != nil {
		c.Logger.Error("删除角色失败", zap.Error(err))
		c.ErrorWithMessage(ctx, "删除失败")
		return
	}

	c.SuccessWithMessage(ctx, "删除成功")
}

// Optionselect 获取角色选择框列表
func (c *SysRoleController) Optionselect(ctx *gin.Context) {
	var roles []domain.SysRole
	if err := c.db.Where("status = ? AND del_flag = ?", "0", "0").Find(&roles).Error; err != nil {
		c.Logger.Error("查询角色选择框列表失败", zap.Error(err))
		c.ErrorWithMessage(ctx, "查询失败")
		return
	}

	c.SuccessWithData(ctx, roles)
}

// AllocatedList 查询已分配用户角色列表
func (c *SysRoleController) AllocatedList(ctx *gin.Context) {
	c.SuccessWithMessage(ctx, "已分配用户列表功能开发中")
}

// UnallocatedList 查询未分配用户角色列表
func (c *SysRoleController) UnallocatedList(ctx *gin.Context) {
	c.SuccessWithMessage(ctx, "未分配用户列表功能开发中")
}

// CancelAuthUser 取消授权用户
func (c *SysRoleController) CancelAuthUser(ctx *gin.Context) {
	c.SuccessWithMessage(ctx, "取消授权功能开发中")
}

// CancelAuthUserAll 批量取消授权用户
func (c *SysRoleController) CancelAuthUserAll(ctx *gin.Context) {
	c.SuccessWithMessage(ctx, "批量取消授权功能开发中")
}

// SelectAuthUserAll 批量选择用户授权
func (c *SysRoleController) SelectAuthUserAll(ctx *gin.Context) {
	c.SuccessWithMessage(ctx, "批量授权功能开发中")
}

// DeptTree 获取部门树
func (c *SysRoleController) DeptTree(ctx *gin.Context) {
	c.SuccessWithMessage(ctx, "部门树功能开发中")
}
